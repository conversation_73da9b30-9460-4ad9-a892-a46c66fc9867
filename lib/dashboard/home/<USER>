import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:gap/gap.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:mialamobile/dashboard/home/<USER>/home_box.dart';
import 'package:mialamobile/dashboard/home/<USER>/recent_delivery.dart';
import 'package:mialamobile/dashboard/home/<USER>';
import 'package:mialamobile/providers/auth_provider.dart';
import 'package:mialamobile/utils/delivery_utils.dart';
import 'package:mialamobile/components/notification_badge.dart';

class HomeTab extends StatefulWidget {
  final VoidCallback? onNavigateToServices;

  const HomeTab({super.key, this.onNavigateToServices});

  @override
  State<HomeTab> createState() => _HomeTabState();
}

class _HomeTabState extends State<HomeTab> {
  @override
  void initState() {
    super.initState();
    // Fetch DVA information, delivery summary, and deliveries when the home tab loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      authProvider.fetchDvaInfo();
      authProvider.fetchDeliverySummary().then((success) {
        if (!success && authProvider.deliverySummaryError != null && mounted) {
          // Show snackbar for delivery summary errors
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Failed to load delivery summary: ${authProvider.deliverySummaryError}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
      authProvider.fetchDeliveries().then((success) {
        if (!success && authProvider.deliveriesError != null && mounted) {
          // Show snackbar for deliveries errors
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                  'Failed to load deliveries: ${authProvider.deliveriesError}'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: const Color(0xff121212),
        body: SafeArea(
            child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 34),
            child: Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                return Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            const CircleAvatar(
                              radius: 20,
                              backgroundColor: Color(0xFF444444),
                              child: Icon(
                                Icons.person,
                                color: Colors.white70,
                                size: 24,
                              ),
                            ),
                            const Gap(10),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Welcome Back!',
                                    style: GoogleFonts.poppins(
                                        textStyle: const TextStyle(
                                            color: Color(0xffFFFFFF),
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600))),
                              ],
                            )
                          ],
                        ),
                        NotificationBadge(
                          iconPath: 'assets/icons/notification.svg',
                          badgeCount: authProvider.unreadNotificationsCount,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const NotificationPage(),
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    const Gap(30),
                    // DVA Information Section
                    if (authProvider.isLoadingDva)
                      const Center(
                        child: CircularProgressIndicator(
                          color: Color(0xffB10303),
                        ),
                      )
                    else if (authProvider.dvaError != null)
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border:
                              Border.all(color: Colors.red.withOpacity(0.3)),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.error_outline,
                                color: Colors.red, size: 20),
                            const Gap(8),
                            Expanded(
                              child: Text(
                                authProvider.dvaError!,
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    else if (authProvider.dvaInfo != null)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Account Name
                                  Text('Account Name',
                                      style: GoogleFonts.poppins(
                                          textStyle: const TextStyle(
                                              color: Color(0xff8C8C8C),
                                              fontSize: 10,
                                              fontWeight: FontWeight.w400))),
                                  const Gap(5),
                                  Text(authProvider.dvaInfo!.accountName,
                                      style: GoogleFonts.poppins(
                                          textStyle: const TextStyle(
                                              color: Color(0xffFFFFFF),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600))),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Account Number
                                  Text('Account Number',
                                      style: GoogleFonts.poppins(
                                          textStyle: const TextStyle(
                                              color: Color(0xff8C8C8C),
                                              fontSize: 10,
                                              fontWeight: FontWeight.w400))),
                                  const Gap(5),
                                  Text(authProvider.dvaInfo!.accountNumber,
                                      style: GoogleFonts.poppins(
                                          textStyle: const TextStyle(
                                              color: Color(0xffFFFFFF),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600))),
                                ],
                              )
                            ],
                          ),
                          const Gap(15),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Account Name
                                  Row(
                                    children: [
                                      Text('Current Balance',
                                          style: GoogleFonts.poppins(
                                              textStyle: const TextStyle(
                                                  color: Color(0xff8C8C8C),
                                                  fontSize: 10,
                                                  fontWeight:
                                                      FontWeight.w400))),
                                      const Gap(5),
                                      SvgPicture.asset('assets/icons/view.svg'),
                                    ],
                                  ),
                                  const Gap(5),
                                  Text(
                                      '₦${authProvider.dvaInfo!.balance.toStringAsFixed(2)}',
                                      style: GoogleFonts.poppins(
                                          textStyle: const TextStyle(
                                              color: Color(0xffFFFFFF),
                                              fontSize: 32,
                                              fontWeight: FontWeight.w600))),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Account Number
                                  Text('Bank Name',
                                      style: GoogleFonts.poppins(
                                          textStyle: const TextStyle(
                                              color: Color(0xff8C8C8C),
                                              fontSize: 10,
                                              fontWeight: FontWeight.w400))),
                                  const Gap(5),
                                  Text(authProvider.dvaInfo!.bankName,
                                      style: GoogleFonts.poppins(
                                          textStyle: const TextStyle(
                                              color: Color(0xffFFFFFF),
                                              fontSize: 16,
                                              fontWeight: FontWeight.w600))),
                                ],
                              )
                            ],
                          ),
                        ],
                      )
                    else
                      // Fallback when no DVA info is available
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text('Current Balance',
                                  style: GoogleFonts.poppins(
                                      textStyle: const TextStyle(
                                          color: Color(0xff8C8C8C),
                                          fontSize: 10,
                                          fontWeight: FontWeight.w400))),
                              const Gap(5),
                              SvgPicture.asset('assets/icons/view.svg'),
                            ],
                          ),
                          const Gap(5),
                          Text('₦0.00',
                              style: GoogleFonts.poppins(
                                  textStyle: const TextStyle(
                                      color: Color(0xffFFFFFF),
                                      fontSize: 32,
                                      fontWeight: FontWeight.w600))),
                        ],
                      ),
                    const Gap(30),

                    // Delivery Summary Error Section
                    if (authProvider.deliverySummaryError != null)
                      Column(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: Colors.red.withOpacity(0.3)),
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.error_outline,
                                    color: Colors.red, size: 20),
                                const Gap(8),
                                Expanded(
                                  child: Text(
                                    'Delivery Summary: ${authProvider.deliverySummaryError!}',
                                    style: GoogleFonts.poppins(
                                      fontSize: 12,
                                      color: Colors.red,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Gap(16),
                        ],
                      ),

                    // First row of HomeBox components
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Completed Delivery Box
                        Expanded(
                          child: authProvider.isLoadingDeliverySummary
                              ? Container(
                                  height: 120,
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: const Color(0xff1E1E1E),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                      color: Color(0xffB10303),
                                      strokeWidth: 2,
                                    ),
                                  ),
                                )
                              : HomeBox(
                                  iconPath: 'assets/icons/completed.svg',
                                  number: authProvider
                                          .deliverySummary?.deliveredCount
                                          .toString() ??
                                      '0',
                                  title: 'Completed Delivery',
                                  subtitle:
                                      'Total successful deliveries made today',
                                ),
                        ),
                        const SizedBox(width: 12),
                        // Pending Delivery Box
                        Expanded(
                          child: authProvider.isLoadingDeliverySummary
                              ? Container(
                                  height: 120,
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: const Color(0xff1E1E1E),
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                  child: const Center(
                                    child: CircularProgressIndicator(
                                      color: Color(0xffB10303),
                                      strokeWidth: 2,
                                    ),
                                  ),
                                )
                              : HomeBox(
                                  iconPath: 'assets/icons/failed.svg',
                                  number: authProvider
                                          .deliverySummary?.pendingCount
                                          .toString() ??
                                      '0',
                                  title: 'Pending Delivery',
                                  subtitle:
                                      'Number of deliveries pending today',
                                ),
                        ),
                      ],
                    ),
                    const Gap(12),

                    // Second row of HomeBox components
                    const Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Total Earnings Box
                        Expanded(
                          child: HomeBox(
                            iconPath: 'assets/icons/money_recieved.svg',
                            number: '₦50,000',
                            title: 'Total Amount Recieved',
                            subtitle:
                                'Total amount recieved from all completed deliveries.',
                          ),
                        ),
                        SizedBox(width: 12),
                        // Customer Ratings Box
                        Expanded(
                          child: HomeBox(
                            iconPath: 'assets/icons/fee_recieved.svg',
                            number: '₦5,000',
                            title: 'Delivery Fee Collected',
                            subtitle: 'Total successful deliveries made today.',
                          ),
                        ),
                      ],
                    ),

                    const Gap(30),

                    // Recent Deliveries section title
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Text(
                              'Recent Deliveries',
                              style: GoogleFonts.poppins(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Colors.white,
                              ),
                            ),
                            const Gap(5),
                            SvgPicture.asset(
                                'assets/icons/package_delivered.svg'),
                          ],
                        ),
                        TextButton(
                          onPressed: () {
                            // Navigate to Services tab to see all deliveries
                            widget.onNavigateToServices?.call();
                          },
                          child: Text(
                            'See All',
                            style: GoogleFonts.poppins(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xffB10303),
                            ),
                          ),
                        ),
                      ],
                    ),

                    // Recent deliveries list
                    const Gap(16),
                    if (authProvider.isLoadingDeliveries)
                      const Center(
                        child: Padding(
                          padding: EdgeInsets.all(20),
                          child: CircularProgressIndicator(
                            color: Color(0xffB10303),
                          ),
                        ),
                      )
                    else if (authProvider.deliveriesError != null)
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.red.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                          border:
                              Border.all(color: Colors.red.withOpacity(0.3)),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.error_outline,
                                color: Colors.red, size: 20),
                            const Gap(8),
                            Expanded(
                              child: Text(
                                'Failed to load deliveries: ${authProvider.deliveriesError}',
                                style: GoogleFonts.poppins(
                                  fontSize: 12,
                                  color: Colors.red,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    else if (authProvider.deliveries.isEmpty)
                      Container(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          children: [
                            Icon(
                              Icons.inbox_outlined,
                              size: 48,
                              color: Colors.grey[600],
                            ),
                            const Gap(12),
                            Text(
                              'No deliveries yet',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                                color: Colors.grey[600],
                              ),
                            ),
                            const Gap(4),
                            Text(
                              'Your recent deliveries will appear here',
                              style: GoogleFonts.poppins(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    else
                      ...DeliveryUtils.limitToRecent(authProvider.deliveries,
                              limit: 5)
                          .map((delivery) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 12),
                          child: RecentDelivery(
                            productName: delivery.productName,
                            trackingId: delivery.deliveryCode,
                            deliveryAddress: delivery.receiverAddress,
                            deliveryDate: DeliveryUtils.formatDeliveryDate(
                                delivery.dueDate),
                            deliveryTime: DeliveryUtils.formatDeliveryTime(
                                delivery.uploadDate),
                            deliveryStatus: DeliveryUtils.formatDeliveryStatus(
                                delivery.deliveryStatus),
                            statusColor: DeliveryUtils.getStatusColor(
                                delivery.deliveryStatus),
                          ),
                        );
                      }),
                  ],
                );
              },
            ),
          ),
        )));
  }
}
