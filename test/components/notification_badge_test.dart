import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mialamobile/components/notification_badge.dart';

void main() {
  group('NotificationBadge Tests', () {
    testWidgets('should display notification icon without badge when count is 0',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationBadge(
              iconPath: 'assets/icons/notification.svg',
              badgeCount: 0,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(NotificationBadge), findsOneWidget);
      // Badge should not be visible when count is 0
      expect(find.text('0'), findsNothing);
    });

    testWidgets('should display badge with count when count > 0',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationBadge(
              iconPath: 'assets/icons/notification.svg',
              badgeCount: 5,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(NotificationBadge), findsOneWidget);
      expect(find.text('5'), findsOneWidget);
    });

    testWidgets('should display "9+" when count is greater than 9',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationBadge(
              iconPath: 'assets/icons/notification.svg',
              badgeCount: 15,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(NotificationBadge), findsOneWidget);
      expect(find.text('9+'), findsOneWidget);
    });

    testWidgets('should display "99+" when count is greater than 99',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationBadge(
              iconPath: 'assets/icons/notification.svg',
              badgeCount: 150,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(NotificationBadge), findsOneWidget);
      expect(find.text('99+'), findsOneWidget);
    });

    testWidgets('should handle tap events correctly',
        (WidgetTester tester) async {
      // Arrange
      bool tapped = false;
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationBadge(
              iconPath: 'assets/icons/notification.svg',
              badgeCount: 3,
              onTap: () {
                tapped = true;
              },
            ),
          ),
        ),
      );

      // Act
      await tester.tap(find.byType(NotificationBadge));
      await tester.pump();

      // Assert
      expect(tapped, true);
    });

    testWidgets('should use custom icon size when provided',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationBadge(
              iconPath: 'assets/icons/notification.svg',
              badgeCount: 1,
              iconSize: 32.0,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(NotificationBadge), findsOneWidget);
      // The widget should be created successfully with custom size
    });

    testWidgets('should apply custom icon color when provided',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationBadge(
              iconPath: 'assets/icons/notification.svg',
              badgeCount: 2,
              iconColor: Colors.blue,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(NotificationBadge), findsOneWidget);
      // The widget should be created successfully with custom color
    });

    testWidgets('should have correct badge styling',
        (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: NotificationBadge(
              iconPath: 'assets/icons/notification.svg',
              badgeCount: 7,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(NotificationBadge), findsOneWidget);
      expect(find.text('7'), findsOneWidget);
      
      // Find the badge container
      final badgeContainer = tester.widget<Container>(
        find.descendant(
          of: find.byType(NotificationBadge),
          matching: find.byType(Container),
        ).first,
      );
      
      // Verify badge styling
      final decoration = badgeContainer.decoration as BoxDecoration;
      expect(decoration.color, const Color(0xffB10303)); // App's red theme
      expect(decoration.shape, BoxShape.circle);
    });
  });
}
